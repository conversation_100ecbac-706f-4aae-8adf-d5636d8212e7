import { logger } from "../../../helpers/logger.js";

export interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  tokenId?: string; // The tokenId (data[0].id) for deletion - format: t1_tok_*
  amount: number;
  description?: string;
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

export interface TokenPaymentValidationResult {
  isValid: boolean;
  errors?: string[];
  sanitizedData?: TokenPaymentRequest;
}

export function validateTokenPaymentRequest(body: string | null): TokenPaymentValidationResult {
  if (!body) {
    return {
      isValid: false,
      errors: ["Request body is required"],
    };
  }

  let parsedData: TokenPaymentRequest;
  try {
    parsedData = JSON.parse(body);
  } catch {
    return {
      isValid: false,
      errors: ["Invalid JSON in request body"],
    };
  }

  const errors: string[] = [];

  if (!parsedData.merchantId || !parsedData.token || parsedData.amount === undefined || parsedData.amount === null) {
    const missingFields: string[] = [];
    if (!parsedData.merchantId) missingFields.push("merchantId");
    if (!parsedData.token) missingFields.push("token");
    if (parsedData.amount === undefined || parsedData.amount === null) missingFields.push("amount");

    errors.push(`Missing required fields: ${missingFields.join(", ")}`);
  }

  const merchantIdValidation = validateMerchantId(parsedData.merchantId);
  if (!merchantIdValidation.isValid) {
    errors.push(merchantIdValidation.error!);
  }

  const tokenValidation = validateToken(parsedData.token);
  if (!tokenValidation.isValid) {
    errors.push(tokenValidation.error!);
  }

  const amountValidation = validateAmount(parsedData.amount);
  if (!amountValidation.isValid) {
    errors.push(amountValidation.error!);
  }

  if (errors.length > 0) {
    return {
      isValid: false,
      errors,
    };
  }

  return {
    isValid: true,
    sanitizedData: parsedData,
  };
}

export interface FieldValidationResult {
  isValid: boolean;
  error?: string;
}

export function validateMerchantId(merchantId: string): FieldValidationResult {
  if (typeof merchantId !== "string" || merchantId.trim().length === 0) {
    return {
      isValid: false,
      error: "merchantId must be a non-empty string",
    };
  }
  return { isValid: true };
}

export function validateToken(token: string): FieldValidationResult {
  if (typeof token !== "string" || token.trim().length === 0) {
    return {
      isValid: false,
      error: "token must be a non-empty string",
    };
  }
  return { isValid: true };
}

export function validateAmount(amount: number): FieldValidationResult {
  if (typeof amount !== "number" || amount < 0) {
    return {
      isValid: false,
      error: "amount must be a non-negative number in cents (0 allowed for $0 authorization)",
    };
  }
  return { isValid: true };
}

export function logValidationResult(validationResult: TokenPaymentValidationResult, requestId?: string): void {
  if (!validationResult.isValid) {
    logger.warn("Token payment validation failed", {
      requestId,
      errors: validationResult.errors,
    });
  } else {
    logger.info("Token payment validation successful", {
      requestId,
      merchantId: validationResult.sanitizedData?.merchantId ? "***" + validationResult.sanitizedData.merchantId.slice(-4) : "MISSING",
      token: validationResult.sanitizedData?.token ? "***" + validationResult.sanitizedData.token.slice(-4) : "MISSING",
      amount: validationResult.sanitizedData?.amount,
      hasCustomerInfo: !!validationResult.sanitizedData?.customerInfo,
    });
  }
}
