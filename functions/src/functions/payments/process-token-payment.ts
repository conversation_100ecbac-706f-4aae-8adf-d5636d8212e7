import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { logger } from "../../helpers/logger.js";
import { validateTokenPaymentRequest, logValidationResult } from "./validators/token-payment.validator.js";
import { processPayment } from "./services/token-payment-processor.service.js";
import {
  createValidationErrorResponse,
  createMerchantValidationErrorResponse,
  createPaymentErrorResponse,
  createSuccessResponse,
  createInternalErrorResponse,
} from "./utils/response-formatter.js";

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;

  try {
    logger.info("Process token payment request", {
      requestId,
      hasBody: !!event.body,
    });

    const validationResult = validateTokenPaymentRequest(event.body);
    logValidationResult(validationResult, requestId);

    if (!validationResult.isValid) {
      return createValidationErrorResponse(validationResult);
    }

    const paymentData = validationResult.sanitizedData!;

    logger.info("Processing token payment", {
      requestId,
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, 8) + "...",
      amount: paymentData.amount,
      description: paymentData.description,
    });

    const processingResult = await processPayment(paymentData);

    if (!processingResult.success) {
      if (processingResult.error?.includes("Merchant validation failed") || processingResult.error?.includes("Invalid or inactive merchant")) {
        return createMerchantValidationErrorResponse(paymentData.merchantId, processingResult.error);
      }

      return createPaymentErrorResponse(paymentData.merchantId, paymentData.amount, processingResult.error!);
    }

    logger.info("Token payment completed successfully", {
      requestId,
      merchantId: paymentData.merchantId,
      transactionId: processingResult.transaction?.id,
      amount: paymentData.amount,
    });

    return createSuccessResponse(processingResult);
  } catch (error) {
    logger.error("Unexpected error processing token payment", {
      requestId,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });

    return createInternalErrorResponse();
  }
};
