import { useEffect, useRef, useState } from "react";
import { PayFieldsConfig } from "./types/payfields.types";

interface GooglePayButtonProps {
  config: PayFieldsConfig;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  disabled?: boolean;
  className?: string;
}

export const GooglePayButton = ({ config, onSuccess: _onSuccess, onFailure, disabled = false, className = "" }: GooglePayButtonProps) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const [isGooglePayReady, setIsGooglePayReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!config.googlePay?.enabled || disabled) {
      return;
    }

    const initializeGooglePay = () => {
      if (!window.PayFields) {
        setError("PayFields not loaded");
        return;
      }

      try {
        // Google Pay button will be automatically rendered by PayFields
        // when googlePay.enabled is true and the container exists
        console.log("Google Pay button container ready:", {
          containerId: "googlePayButton",
          enabled: config.googlePay?.enabled,
          environment: config.googlePay?.environment,
        });

        setIsGooglePayReady(true);
        setError(null);
      } catch (err) {
        console.error("Failed to initialize Google Pay:", err);
        setError("Failed to initialize Google Pay");
        if (onFailure) {
          onFailure(err);
        }
      }
    };

    // Wait for PayFields to be ready
    if (window.PayFields) {
      initializeGooglePay();
    } else {
      // Listen for PayFields script load
      const checkPayFields = setInterval(() => {
        if (window.PayFields) {
          clearInterval(checkPayFields);
          initializeGooglePay();
        }
      }, 100);

      return () => clearInterval(checkPayFields);
    }
  }, [config, disabled, onFailure]);

  if (!config.googlePay?.enabled) {
    return null;
  }

  if (error) {
    return <div className={`p-3 bg-red-50 text-red-700 rounded-md text-sm ${className}`}>{error}</div>;
  }

  return (
    <div className={`google-pay-container ${className}`}>
      <div className="mb-3">
        <div className="flex items-center justify-center">
          <div className="flex-1 border-t border-gray-200"></div>
          <span className="px-3 text-sm text-gray-500 bg-white">or</span>
          <div className="flex-1 border-t border-gray-200"></div>
        </div>
      </div>

      <div
        id="googlePayButton"
        ref={buttonRef}
        className={`google-pay-button-container ${disabled ? "opacity-50 pointer-events-none" : ""}`}
        style={{
          minHeight: "48px",
          display: "block",
          width: "100%",
          borderRadius: "8px",
          overflow: "hidden",
        }}
      >
        {!isGooglePayReady && (
          <div className="flex items-center justify-center h-12 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200 shadow-sm">
            <div className="animate-spin h-4 w-4 border-2 border-t-transparent border-gray-600 rounded-full"></div>
            <span className="ml-2 text-sm text-gray-600 font-medium">Loading Google Pay...</span>
          </div>
        )}
      </div>
    </div>
  );
};
