import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "sonner";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { loadAllPaymentScripts, cleanupPayFieldsScript } from "../utils/script-loader";
import { initializePayFields, updateBillingAddress } from "../utils/payfields-initializer";
import {
  createPaymentSuccessHandler,
  createPaymentFailureHandler,
  createValidationFailureHandler,
  createPaymentFinishHandler,
} from "../handlers/payment-handlers";
import { postMessageToParent } from "../utils/iframe-communication";

interface UsePayFieldsOptions {
  config: PayFieldsConfig;
  paymentInfo?: PaymentInfo | null;
  billingAddress?: BillingAddress;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
}

interface UsePayFieldsReturn {
  loaded: boolean;
  scriptError: string | null;
  isSubmitting: boolean;
  validationError: string | null;
  handleSubmit: () => void;
}

export const usePayFields = ({ config, paymentInfo, billingAddress, onSuccess, onFailure }: UsePayFieldsOptions): UsePayFieldsReturn => {
  const [loaded, setLoaded] = useState(false);
  const [scriptError, setScriptError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [payFieldsInitialized, setPayFieldsInitialized] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const handlePaymentSuccess = useCallback(
    async (response: unknown) => {
      setIsSubmitting(false);
      setValidationError(null);

      try {
        const handler = createPaymentSuccessHandler(config, paymentInfo || null, billingAddress, onSuccess);
        await handler(response as PaymentResponse);
      } catch (error) {
        setValidationError(error instanceof Error ? error.message : "Payment processing failed");
      }
    },
    [config, billingAddress, onSuccess]
  );

  const handlePaymentFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createPaymentFailureHandler(onFailure);
      handler(err as PaymentError);
      setValidationError((err as PaymentError)?.message || "Payment processing failed");
    },
    [onFailure]
  );

  const handleValidationFailure = useCallback(
    (err: unknown) => {
      setIsSubmitting(false);
      const handler = createValidationFailureHandler(onFailure);
      handler(err);
      setValidationError("Payment validation failed. Please check your card details.");
    },
    [onFailure]
  );

  const handlePaymentFinish = useCallback((response: unknown) => {
    setIsSubmitting(false);
    const handler = createPaymentFinishHandler();
    handler(response);
  }, []);

  useEffect(() => {
    if (!config) {
      setScriptError("No payment configuration provided");
      return;
    }

    console.log("PayFields configuration received:", {
      merchantId: config.merchantId,
      publicKey: config.publicKey ? "***" + config.publicKey.slice(-4) : "NOT_SET",
      amount: config.amount,
      mode: config.mode,
      txnType: config.txnType,
      description: config.description,
    });
  }, [config]);

  useEffect(() => {
    if (!config) return;

    loadAllPaymentScripts({
      onLoad: () => setLoaded(true),
      onError: () => {
        setScriptError("Failed to load payment processing script. Please try again later.");
        setIsSubmitting(false);
        toast.error("Failed to load payment processor");
      },
    }).catch((error: Error) => {
      setScriptError(error.message);
    });

    return () => {
      cleanupPayFieldsScript();
    };
  }, [config]);

  useEffect(() => {
    if (!loaded || !window.PayFields || !config || payFieldsInitialized) return;

    console.log("Initializing PayFields with:", {
      merchantId: config.merchantId,
      amount: config.amount,
      mode: config.mode,
      txnType: config.txnType,
    });

    setTimeout(() => {
      try {
        const enhancedOnFailure = (err: unknown) => {
          console.error("PayFields initialization or validation error:", err);

          const error = err as PaymentError;
          if (error && error.message) {
            if (error.message.includes("API key") || error.message.includes("apiKey")) {
              console.error("❌ API Key Issue: Check if your publicKey is valid for the current environment");
              setScriptError("Invalid API key. Please check your payment configuration.");
            } else if (error.message.includes("merchant") || error.message.includes("Merchant")) {
              console.error("❌ Merchant ID Issue: Check if your merchantId is correct");
              setScriptError("Invalid merchant ID. Please check your payment configuration.");
            } else if (error.message.includes("unauthorized") || error.message.includes("forbidden")) {
              console.error("❌ Authorization Issue: Check environment (sandbox vs production)");
              setScriptError("Payment authorization failed. Check your credentials and environment.");
            } else {
              console.error("❌ General PayFields Error:", error.message);
              setScriptError(`Payment initialization failed: ${error.message}`);
            }
          } else {
            console.error("❌ Unknown PayFields Error:", err);
            setScriptError("Payment initialization failed with unknown error.");
          }

          handlePaymentFailure(err);
        };

        initializePayFields(config, {
          onSuccess: handlePaymentSuccess,
          onFailure: enhancedOnFailure,
          onValidationFailure: handleValidationFailure,
          onFinish: handlePaymentFinish,
          billingAddress,
        });

        setPayFieldsInitialized(true);
      } catch (error) {
        console.error("Error initializing PayFields:", error);
        setScriptError("Error initializing payment processor. Please try again later.");
        setIsSubmitting(false);
        toast.error("Payment configuration error");
      }
    }, 500);
  }, [
    loaded,
    payFieldsInitialized,
    config,
    billingAddress,
    handlePaymentSuccess,
    handlePaymentFailure,
    handleValidationFailure,
    handlePaymentFinish,
  ]);

  useEffect(() => {
    if (!payFieldsInitialized || !window.PayFields) return;

    console.log("Updating PayFields callbacks");

    try {
      window.PayFields.onSuccess = handlePaymentSuccess;
      window.PayFields.onFailure = handlePaymentFailure;
      window.PayFields.onValidationFailure = handleValidationFailure;
      window.PayFields.onFinish = handlePaymentFinish;

      console.log("PayFields callbacks updated successfully");
    } catch (error) {
      console.error("Error updating PayFields callbacks:", error);
    }
  }, [payFieldsInitialized, handlePaymentSuccess, handlePaymentFailure, handleValidationFailure, handlePaymentFinish]);

  useEffect(() => {
    if (!window.PayFields || !billingAddress) return;
    updateBillingAddress(billingAddress);
  }, [billingAddress]);

  const handleSubmit = useCallback(() => {
    if (!window.PayFields || isSubmitting || !config) return;

    console.log("Submitting secure payment...");
    setIsSubmitting(true);
    setValidationError(null);

    postMessageToParent("PAYMENT_SUBMISSION_STARTED");

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isSubmitting) {
        console.warn("Payment submission timed out");
        setIsSubmitting(false);
        const timeoutMessage = "Payment processing timed out. Please try again.";
        toast.error(timeoutMessage);
        postMessageToParent("PAYMENT_TIMEOUT", { error: timeoutMessage });
        if (onFailure) onFailure({ message: timeoutMessage });
      }
    }, 30000);

    try {
      window.PayFields.submit();
    } catch (error) {
      console.error("Error submitting payment:", error);
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      setIsSubmitting(false);
      const errorMessage = "Error processing payment. Please try again.";
      toast.error(errorMessage);
      postMessageToParent("PAYMENT_SUBMISSION_ERROR", {
        error: errorMessage,
        details: error,
      });
      if (onFailure) onFailure({ message: errorMessage });
    }
  }, [config, isSubmitting, onFailure]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    loaded,
    scriptError,
    isSubmitting,
    validationError,
    handleSubmit,
  };
};
