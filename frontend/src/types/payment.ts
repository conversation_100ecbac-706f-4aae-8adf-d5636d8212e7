export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  googlePay?: {
    enabled: boolean;
    environment?: "TEST" | "PRODUCTION";
  };
}

export interface MerchantInfo {
  id: string;
  name: string;
  status: number;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
}

export interface PaymentInfo {
  description: string;
  amount: number;
  currency?: string;
  returnUrl?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  taxAmount?: number;
  orderNumber?: string;
}

export interface BillingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export interface PaymentIframeData {
  config: PayFieldsConfig;
  merchantInfo: MerchantInfo;
  paymentInfo: PaymentInfo;
}

export interface PaymentIframeState {
  payFieldsConfig: PayFieldsConfig | null;
  merchantInfo: MerchantInfo | null;
  paymentInfo: PaymentInfo | null;
  error: string | null;
  success: boolean;
  loading: boolean;
  billingAddress: BillingAddress;
  termsAccepted: boolean;
}
