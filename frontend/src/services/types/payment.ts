export interface PaymentConfigRequest {
  merchantId: string;
  description: string;
  amount?: number;
}

export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
  googlePay?: {
    enabled: boolean;
    environment?: "TEST" | "PRODUCTION";
  };
}

export interface PaymentConfigResponse {
  config: PayFieldsConfig;
  message: string;
  merchantInfo: {
    id: string;
    name: string;
    status: number;
  };
}

export interface GenerateIntegrationTokenRequest {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number;
}

export interface GenerateIntegrationTokenResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    expiresAt: string;
    embedUrl: string;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
  };
}

export interface ValidateIframeTokenResponse {
  success: boolean;
  message: string;
  data?: {
    config: PayFieldsConfig;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
    paymentInfo: {
      description: string;
      amount: number;
      returnUrl?: string;
    };
  };
}

export interface IframeConfigParams {
  domain?: string;
  theme?: "light" | "dark" | "auto";
  language?: string;
  currency?: string;
}

export interface IframeConfigResponse {
  success: boolean;
  message: string;
  data: {
    payrixConfig: {
      scriptUrl: string;
      environment: string;
      supportedFeatures: string[];
    };
    styling: {
      theme: string;
      customCSS: Record<string, string>;
    };
    security: {
      allowedOrigins: string[];
      cspDirectives: string[];
    };
    features: {
      autoResize: boolean;
      responsiveDesign: boolean;
      mobileOptimized: boolean;
    };
  };
}

export interface TokenStatusResponse {
  success: boolean;
  message: string;
  data: {
    isValid: boolean;
    status: "valid" | "expired" | "used" | "invalid";
    expiresAt?: string;
    timeRemaining?: number;
    merchantId?: string;
    amount?: number;
    description?: string;
  };
}

export interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  tokenId?: string; // The tokenId (data[0].id) for deletion - format: t1_tok_*
  amount: number;
  description?: string;
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

export interface TokenPaymentResponse {
  success: boolean;
  message: string;
  transaction?: {
    id: string;
    status: string;
    amount: number;
    merchantId: string;
    description: string;
    createdAt: string;
  };
  merchantInfo?: {
    id: string;
    name: string;
    status: number;
  };
  error?: string;
}
